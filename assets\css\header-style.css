@font-face {
    font-family: 'Itim';
    src: url('assets/fonts/Itim-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
.font-itim {
    font-family: 'Itim', cursive;
}
.fade-in {
    opacity: 0;
    transform: scale(0.95);
    animation: fadeInScale 0.3s ease-out forwards;
}
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
    .glass {
      background: rgba(255,255,255,0.7);
      backdrop-filter: blur(12px);
      box-shadow: 0 8px 32px 0 rgba(31,38,135,0.15);
    }
    .fade-in {
      animation: fadeIn 0.4s cubic-bezier(.4,0,.2,1) both;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-16px) scale(0.98);}
      to { opacity: 1; transform: translateY(0) scale(1);}
    }
    .dropdown-anim {
      animation: dropdownAnim 0.25s cubic-bezier(.4,0,.2,1) both;
    }
    @keyframes dropdownAnim {
      from { opacity: 0; transform: translateY(-10px);}
      to { opacity: 1; transform: translateY(0);}
    }
    .modal-fade {
      animation: modalFade 0.3s both;
    }
    @keyframes modalFade {
      from { opacity: 0; }
      to { opacity: 1; }
    }