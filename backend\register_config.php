<?php
session_start();
require_once 'config.php'; // include your DB connection (adjust path if needed)

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $profile_image = 'assets/images/default-avatar.png';

    // Add user_type support (default to 'user' if not provided or invalid)
    $user_type = 'user';
    if (isset($_POST['user_type']) && in_array($_POST['user_type'], ['admin', 'user'])) {
        $user_type = $_POST['user_type'];
    }

    // Check for duplicate username/email
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $email);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        $_SESSION['register_error'] = 'ชื่อผู้ใช้หรืออีเมลถูกใช้ไปแล้ว';
        header('Location: ../index.php');
        exit;
    }
    $stmt->close();

    // Handle image upload
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($ext, $allowed)) {
            $newName = 'profile_' . uniqid() . '.' . $ext;
            $uploadPath = 'assets/images/' . $newName;
            // ปรับ path ให้ถูกต้อง (upload ไปที่โฟลเดอร์ assets/images ใน root)
            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], dirname(__DIR__) . '/' . $uploadPath)) {
                $profile_image = $uploadPath;
            }
        }
    }

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Insert user (add user_type)
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, profile_image, user_type) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("sssss", $username, $email, $hashed_password, $profile_image, $user_type);
    if ($stmt->execute()) {
        $_SESSION['username'] = $username;
        $_SESSION['profile_image'] = $profile_image;
        // Optionally store user_type in session
        $_SESSION['user_type'] = $user_type;
        header('Location: ../index.php');
        exit;
    } else {
        $_SESSION['register_error'] = 'เกิดข้อผิดพลาดในการสมัครสมาชิก';
        header('Location: ../index.php');
        exit;
    }
}
header('Location: ../index.php');
exit;
?>
