<?php session_start(); ?>

  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
  <link href="https://fonts.googleapis.com/css2?family=Niramit:wght@500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="assets/css/header-style.css">
  <style>
 
  </style>

</head>
<body class="bg-gradient-to-br from-gray-100 via-blue-50 to-purple-100 min-h-screen">

<!-- Navbar -->
<nav class="glass shadow-lg fixed w-full top-0 left-0 z-40 fade-in">
  <div class="w-full px-4 sm:px-6 py-3 flex items-center justify-between">
    <!-- Logo (ซ้ายสุด) -->
    <a href="index.php" class="flex items-center gap-2">
      <img src="assets/images/default-logo.png" alt="Logo" class="w-12 h-12 sm:w-14 sm:h-14 object-contain rounded-xl shadow-md border border-white/60">
      <span class="text-lg sm:text-xl font-bold text-gray-700 tracking-wide drop-shadow">CardShop</span>
    </a>
    <!-- Hamburger for mobile -->
    <button id="mobileMenuBtn" class="sm:hidden flex items-center px-2 py-2 rounded-lg hover:bg-white/40 transition focus:outline-none" aria-label="Open Menu">
      <svg class="w-7 h-7 text-gray-700" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16"/>
      </svg>
    </button>
    <!-- Desktop Nav -->
    <div class="hidden sm:flex items-center gap-2">
      <nav>
        <ul class="flex space-x-4">
          <li>
            <a href="#" class="text-blue-600 font-semibold px-3 py-2 rounded-lg bg-blue-100 shadow active" aria-current="page">Home</a>
          </li>
          <li>
            <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">Features</a>
          </li>
          <li>
            <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">Pricing</a>
          </li>
          <li>
            <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">FAQs</a>
          </li>
          <li>
            <a href="#" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">About</a>
          </li>
        </ul>
      </nav>
    </div>
    <!-- User Area (ขวาสุด) -->
    <div class="hidden sm:flex items-center gap-4">
      <?php if (isset($_SESSION['username'])): ?>
        <div class="relative">
          <button id="profileBtn" class="flex items-center gap-2 focus:outline-none hover:bg-white/40 px-2 py-1 rounded-lg transition">
            <img src="<?php echo isset($_SESSION['profile_image']) ? $_SESSION['profile_image'] : 'assets/images/default-avatar.png'; ?>" class="w-10 h-10 rounded-full object-cover border-2 border-white shadow" alt="Profile">
            <span class="font-semibold text-gray-800 drop-shadow"><?php echo $_SESSION['username']; ?></span>
            <i class="fas fa-chevron-down text-gray-600"></i>
          </button>
          <ul id="dropdownMenu" class="absolute right-0 mt-2 w-44 glass rounded-xl shadow-xl hidden dropdown-anim z-30">
            <li>
              <a href="backend/logout.php" class="block px-5 py-3 hover:bg-red-100/60 text-sm rounded-b-xl transition">
                <i class="fas fa-sign-out-alt mr-2"></i>ออกจากระบบ
              </a>
            </li>
          </ul>
        </div>
      <?php else: ?>
        <button onclick="openAuthModal()" class="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white drop-shadow px-6 py-2 rounded-xl shadow-lg text-sm font-semibold transition-all duration-200 border border-white/30">
          <i class="fas fa-sign-in-alt mr-1"></i>เข้าสู่ระบบ / สมัครสมาชิก
        </button>
      <?php endif; ?>
    </div>
  </div>
  <!-- Mobile Menu -->
  <div id="mobileMenu" class="sm:hidden hidden px-4 pb-4 pt-2">
    <nav>
      <ul class="flex flex-col space-y-2">
        <li>
          <a href="#" class="block text-blue-600 font-semibold px-3 py-2 rounded-lg bg-blue-100 shadow active" aria-current="page">Home</a>
        </li>
        <li>
          <a href="#" class="block text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">Features</a>
        </li>
        <li>
          <a href="#" class="block text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">Pricing</a>
        </li>
        <li>
          <a href="#" class="block text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">FAQs</a>
        </li>
        <li>
          <a href="#" class="block text-gray-700 hover:text-blue-600 px-3 py-2 rounded-lg transition">About</a>
        </li>
      </ul>
    </nav>
    <div class="mt-4 flex flex-col gap-2">
      <?php if (isset($_SESSION['username'])): ?>
        <div class="relative">
          <button id="mobileProfileBtn" class="flex items-center gap-2 focus:outline-none hover:bg-white/40 px-2 py-1 rounded-lg transition w-full">
            <img src="<?php echo isset($_SESSION['profile_image']) ? $_SESSION['profile_image'] : 'assets/images/default-avatar.png'; ?>" class="w-10 h-10 rounded-full object-cover border-2 border-white shadow" alt="Profile">
            <span class="font-semibold text-gray-800 drop-shadow"><?php echo $_SESSION['username']; ?></span>
            <i class="fas fa-chevron-down text-gray-600"></i>
          </button>
          <ul id="mobileDropdownMenu" class="w-full glass rounded-xl shadow-xl border border-white/40 hidden dropdown-anim z-30 mt-2">
            <li>
              <a href="backend/logout.php" class="block px-5 py-3 hover:bg-red-100/60 text-sm rounded-b-xl transition">
                <i class="fas fa-sign-out-alt mr-2"></i>ออกจากระบบ
              </a>
            </li>
          </ul>
        </div>
      <?php else: ?>
        <button onclick="openAuthModal()" class="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white drop-shadow px-6 py-2 rounded-xl shadow-lg text-sm font-semibold transition-all duration-200 border border-white/30">
          <i class="fas fa-sign-in-alt mr-1"></i>เข้าสู่ระบบ / สมัครสมาชิก
        </button>
      <?php endif; ?>
    </div>
  </div>
</nav>

<!-- Auth Modal -->
<div id="authModal" class="fixed inset-0 bg-black/50 hidden z-50 flex items-center justify-center transition-all duration-200">
  <div class="relative w-full max-w-md mx-auto bg-white rounded-3xl shadow-2xl p-0 overflow-hidden border border-gray-200 modal-fade">
    <!-- Close Button -->
    <button onclick="closeAuthModal()" class="absolute top-4 right-4 w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 hover:bg-red-100 text-gray-500 hover:text-red-500 text-2xl font-bold shadow transition-all duration-150 z-10">
      <span class="sr-only">Close</span>
      &times;
    </button>
    <!-- Modal Content -->
    <div class="px-8 pt-10 pb-8">
      <div class="text-center mb-7">
        <h2 class="text-2xl font-extrabold text-gray-800 tracking-tight mb-1">เข้าสู่ระบบ / สมัครสมาชิก</h2>
        <p class="text-gray-400 text-sm">ยินดีต้อนรับสู่ CardShop</p>
      </div>
      <!-- Tabs -->
      <div class="flex justify-center mb-7 gap-2">
        <button onclick="switchTab('login')" id="loginTab" class="font-semibold px-5 py-2 rounded-full transition-all duration-150 border-2 border-blue-500 text-blue-600 bg-blue-50 shadow-sm focus:outline-none active" style="min-width: 120px;">
          เข้าสู่ระบบ
        </button>
        <button onclick="switchTab('register')" id="registerTab" class="font-semibold px-5 py-2 rounded-full transition-all duration-150 border-2 border-gray-200 text-gray-400 bg-gray-50 shadow-sm focus:outline-none" style="min-width: 120px;">
          สมัครสมาชิก
        </button>
      </div>
      <!-- Login Form -->
      <div id="loginForm">
        <form action="backend/login_config.php" method="post" class="space-y-5">
          <input type="text" name="username" placeholder="ชื่อผู้ใช้" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300 bg-white/90 shadow">
          <input type="password" name="password" placeholder="รหัสผ่าน" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300 bg-white/90 shadow">
          <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 rounded-lg font-semibold hover:from-blue-600 hover:to-purple-600 shadow transition-all">เข้าสู่ระบบ</button>
        </form>
      </div>
      <!-- Register Form -->
      <div id="registerForm" class="hidden">
        <form action="backend/register_config.php" method="post" enctype="multipart/form-data" class="space-y-5">
          <div class="flex justify-center mb-2">
            <img id="register-image-preview" src="assets/images/default-avatar.png" class="w-24 h-24 object-cover rounded-full border-4 border-white shadow-lg bg-white/60" alt="Preview">
          </div>
          <input type="file" name="profile_image" required onchange="showRegisterImagePreview(event)" class="w-full px-4 py-2 border border-gray-200 rounded-lg bg-white/90 shadow">
          <input type="text" name="username" placeholder="ชื่อผู้ใช้" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-300 bg-white/90 shadow">
          <input type="email" name="email" placeholder="อีเมล" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-300 bg-white/90 shadow">
          <input type="password" name="password" placeholder="รหัสผ่าน" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-300 bg-white/90 shadow">
          <button type="submit" class="w-full bg-gradient-to-r from-green-400 to-blue-400 text-white py-3 rounded-lg font-semibold hover:from-green-500 hover:to-blue-500 shadow transition-all">สมัครสมาชิก</button>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
/* ...existing code... */
#authModal .modal-fade {
  animation: modalFadeIn 0.25s;
}
@keyframes modalFadeIn {
  from { transform: translateY(40px) scale(0.97); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}
#authModal .active {
  border-color: #3b82f6 !important;
  background: #eff6ff !important;
  color: #2563eb !important;
}
#authModal button:not(.active) {
  border-color: #e5e7eb !important;
  background: #f9fafb !important;
  color: #9ca3af !important;
}

/* Blue Magic Color Modal Styles */
#colorModal .modal-fade {
  animation: colorModalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
@keyframes colorModalFadeIn {
  from {
    transform: translateY(60px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.color-option.selected .w-full {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

.color-option.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
  z-index: 10;
}

.color-option {
  position: relative;
}

/* Magic sparkle effect */
@keyframes sparkle {
  0% { transform: scale(0) rotate(0deg); opacity: 0; }
  50% { transform: scale(1) rotate(180deg); opacity: 1; }
  100% { transform: scale(0) rotate(360deg); opacity: 0; }
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #60a5fa, #3b82f6);
  border-radius: 50%;
  animation: sparkle 1.5s ease-in-out infinite;
}
</style>

<script>
function openAuthModal() {
  document.getElementById('authModal').classList.remove('hidden');
  setTimeout(() => document.getElementById('authModal').classList.add('modal-fade'), 10);
  document.body.style.overflow = 'hidden';
}
function closeAuthModal() {
  document.getElementById('authModal').classList.add('hidden');
  document.body.style.overflow = '';
}
window.onclick = function(event) {
  const modal = document.getElementById('authModal');
  if (event.target === modal) closeAuthModal();
};

function switchTab(tab) {
  const loginForm = document.getElementById('loginForm');
  const registerForm = document.getElementById('registerForm');
  const loginTab = document.getElementById('loginTab');
  const registerTab = document.getElementById('registerTab');
  if (tab === 'login') {
    loginForm.classList.remove('hidden');
    registerForm.classList.add('hidden');
    loginTab.classList.add('active');
    registerTab.classList.remove('active');
  } else {
    loginForm.classList.add('hidden');
    registerForm.classList.remove('hidden');
    registerTab.classList.add('active');
    loginTab.classList.remove('active');
  }
}

function showRegisterImagePreview(event) {
  const input = event.target;
  const preview = document.getElementById('register-image-preview');
  if (input.files && input.files[0]) {
    const reader = new FileReader();
    reader.onload = function(e) {
      preview.src = e.target.result;
    };
    reader.readAsDataURL(input.files[0]);
  } else {
    preview.src = 'assets/images/default-avatar.png';
  }
}

// Dropdown logic (desktop)
const profileBtn = document.getElementById('profileBtn');
const dropdownMenu = document.getElementById('dropdownMenu');
if (profileBtn && dropdownMenu) {
  profileBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    dropdownMenu.classList.toggle('hidden');
  });
  document.addEventListener('click', function(e) {
    if (!dropdownMenu.classList.contains('hidden')) {
      dropdownMenu.classList.add('hidden');
    }
  });
  dropdownMenu.addEventListener('click', function(e) {
    e.stopPropagation();
  });
}

// Mobile menu toggle
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const mobileMenu = document.getElementById('mobileMenu');
if (mobileMenuBtn && mobileMenu) {
  mobileMenuBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    mobileMenu.classList.toggle('hidden');
  });
  document.addEventListener('click', function(e) {
    if (!mobileMenu.classList.contains('hidden') && !mobileMenu.contains(e.target) && e.target !== mobileMenuBtn) {
      mobileMenu.classList.add('hidden');
    }
  });
}

// Mobile profile dropdown
const mobileProfileBtn = document.getElementById('mobileProfileBtn');
const mobileDropdownMenu = document.getElementById('mobileDropdownMenu');
if (mobileProfileBtn && mobileDropdownMenu) {
  mobileProfileBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    mobileDropdownMenu.classList.toggle('hidden');
  });
  document.addEventListener('click', function(e) {
    if (!mobileDropdownMenu.classList.contains('hidden')) {
      mobileDropdownMenu.classList.add('hidden');
    }
  });
  mobileDropdownMenu.addEventListener('click', function(e) {
    e.stopPropagation();
  });
}
</script>

</body>
</html>
